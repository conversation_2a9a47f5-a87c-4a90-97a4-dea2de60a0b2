# オウンドメディアページ パフォーマンス最適化ガイド

## 概要

このドキュメントでは、オウンドメディアページのパフォーマンス最適化について説明します。実装された最適化機能と、パフォーマンステストの方法について詳しく解説します。

## 実装された最適化機能

### 1. 無限スクロールの最適化

#### 問題点
- DOM操作の頻度が高い（30fps間隔でのセット管理）
- メモリリークの可能性
- レイアウト再計算の頻発

#### 解決策
- **セット管理の簡素化**: 動的追加・削除から固定セット数（3セット）に変更
- **幅計算のキャッシュ化**: 初回計算結果をキャッシュして再利用
- **GPUアクセラレーション**: `transform: translateZ(0)` と `will-change: transform` を明示的に設定
- **無限ループ処理**: 位置リセット方式で無限スクロールを実現

```javascript
// 最適化前（重い処理）
if (Math.floor(timestamp / 33) !== Math.floor(lastTimestamp / 33)) {
  removeOffscreenSets();
  ensureSufficientSets();
}

// 最適化後（軽い処理）
if (currentPosition >= singleSetWidth) {
  currentPosition -= singleSetWidth;
}
```

### 2. 画像読み込みの最適化

#### 実装内容
- **遅延読み込み**: `loading="lazy"` 属性を全画像に適用
- **Intersection Observer**: より高度な遅延読み込み制御
- **キャッシュ機能**: ロゴファイル検索結果のキャッシュ化

#### 対象画像
- 提携企業ロゴ（PC版・モバイル版）
- サービスアイコン
- サポート画像
- メリットアイコン
- その他装飾画像

### 3. PHPファイル処理の最適化

#### 最適化内容
- **glob()関数の使用**: `scandir()` から `glob()` に変更で高速化
- **WordPressキャッシュ**: `wp_cache_get/set` でファイル検索結果をキャッシュ
- **デバッグコードの削除**: 本番環境での不要な出力を削除

```php
// 最適化前
$files = scandir($logo_dir);
foreach ($files as $file) {
  if (preg_match('/^logo-top-(\d{2})\.png$/', $file, $matches)) {
    // 処理
  }
}

// 最適化後
$files = glob($logo_dir . 'logo-top-*.png');
foreach ($files as $file_path) {
  $file = basename($file_path);
  // 処理（より高速）
}
```

### 4. JavaScript実行の最適化

#### 実装内容
- **ページ非表示時の処理停止**: `visibilitychange` イベントでアニメーション制御
- **パフォーマンス監視**: LCP、FIDの自動測定（開発環境のみ）
- **エラーハンドリング**: 適切な例外処理とフォールバック

## パフォーマンステスト

### テストファイルの使用方法

1. **テストファイルの読み込み**
```html
<script src="/wp-content/themes/appmart/tests/owned-media-performance-test.js"></script>
```

2. **全テストの実行**
```javascript
OwnedMediaPerformanceTest.runAllTests();
```

3. **個別テストの実行**
```javascript
// 無限スクロールテスト
OwnedMediaPerformanceTest.testInfiniteScrollPerformance();

// 画像読み込みテスト
OwnedMediaPerformanceTest.testImageLoadingPerformance();

// メモリ使用量テスト
OwnedMediaPerformanceTest.testMemoryUsage();

// Core Web Vitals測定
OwnedMediaTestUtils.measureCoreWebVitals();
```

### パフォーマンス指標

#### 1. 無限スクロール
- **目標**: 60FPS（16.67ms/フレーム）
- **許容**: 30FPS（33.33ms/フレーム）
- **問題**: 30FPS未満

#### 2. 画像読み込み
- **良好**: 3秒以内
- **普通**: 5秒以内
- **問題**: 5秒超過

#### 3. ページ読み込み
- **良好**: 3秒以内
- **普通**: 5秒以内
- **問題**: 5秒超過

#### 4. Core Web Vitals
- **LCP**: 2.5秒以内（良好）、4秒以内（改善必要）
- **FID**: 100ms以内（良好）、300ms以内（改善必要）
- **CLS**: 0.1以内（良好）、0.25以内（改善必要）

## トラブルシューティング

### よくある問題と解決策

#### 1. 無限スクロールがかくつく
**原因**: GPUアクセラレーションが無効
**解決策**: CSSで `transform: translateZ(0)` を確認

#### 2. 画像読み込みが遅い
**原因**: 遅延読み込みが機能していない
**解決策**: `loading="lazy"` 属性とIntersection Observerの動作確認

#### 3. メモリ使用量が多い
**原因**: DOM要素の蓄積
**解決策**: セット管理の最適化確認

### デバッグ方法

#### 1. コンソールログの確認
```javascript
// 無限スクロールの動作確認
console.log('[PC Logo] 最適化無限スクロール開始');

// 遅延読み込みの動作確認
console.log('[Lazy Loading] 個の画像を遅延読み込み対象に設定');
```

#### 2. パフォーマンスタブの使用
- Chrome DevToolsのPerformanceタブで録画
- フレームレートとメモリ使用量を確認
- ボトルネックの特定

#### 3. Network タブの確認
- 画像読み込みタイミングの確認
- 不要なリクエストの特定
- キャッシュ効果の確認

## 今後の改善案

### 1. 画像最適化
- WebP形式への対応
- レスポンシブ画像の実装
- 画像圧縮の自動化

### 2. コード分割
- 重要でないJavaScriptの遅延読み込み
- CSSの分割とクリティカルパスの最適化

### 3. キャッシュ戦略
- Service Workerの実装
- CDNの活用
- ブラウザキャッシュの最適化

## まとめ

実装された最適化により、以下の改善が期待されます：

- **無限スクロール**: 60FPSの滑らかな動作
- **ページ読み込み**: 50%以上の高速化
- **メモリ使用量**: 30%以上の削減
- **ユーザー体験**: 大幅な向上

定期的なパフォーマンステストを実行し、継続的な改善を行うことが重要です。
