// ==================================================
// 提携企業ロゴセクション
// ==================================================
.owned-media-partner-logos {
  @include owned-media-section($owned-media-light-gray);

  width: 100%;
  padding: $owned-media-section-padding-desktop 0;
  overflow: hidden;

  // コンテナ
  &__container {
    @include owned-media-container;

    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: unset;
    padding: 0;
  }

  // 各行のロゴ
  &__row {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    overflow: hidden;
    background-color: $owned-media-white;

    &--top {
      margin-bottom: $owned-media-spacing-small;
    }

    &--bottom {
      margin-top: $owned-media-spacing-small;
    }
  }

  // ロゴトラック（PC・モバイル共通）
  &__track {
    display: flex;
    gap: $owned-media-partner-logos-row-gap;
    align-items: center;
    width: max-content;
    height: 100%;

    // GPUアクセラレーションを有効化
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;

    // PC版・モバイル版ともに無限スクロール対応
    // 中央揃えは削除し、左揃えで無限スクロールを実現
  }

  // ロゴ画像（PC・モバイル共通）
  &__logo {
    @include partner-logo-pc($owned-media-partner-logos-logo-max-height);
  }

  @include media-breakpoint-down(md) {
    height: $owned-media-partner-logos-mobile-height;
    padding: $owned-media-section-padding-mobile 0;

    &__container {
      position: relative;
      width: 100%;
      max-width: none;
      height: $owned-media-partner-logos-mobile-container-height;
      padding: 0;
      overflow: hidden;
    }

    &__row {
      height: $owned-media-partner-logos-mobile-row-height;

      &--top {
        margin-bottom: $owned-media-spacing-small;
      }

      &--bottom {
        margin-top: 0;
        margin-bottom: $owned-media-spacing-large;
      }
    }

    &__track {
      gap: $owned-media-partner-logos-mobile-track-gap;
      justify-content: flex-start;
      width: max-content;
    }

    &__logo {
      @include partner-logo-mobile($owned-media-partner-logos-mobile-logo-max-height);
    }
  }
}
