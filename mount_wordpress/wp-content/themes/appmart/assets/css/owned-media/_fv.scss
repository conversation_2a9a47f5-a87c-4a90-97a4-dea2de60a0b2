// ==================================================
// FVセクション
// ==================================================
.owned-media-fv {
  @include owned-media-section($owned-media-white);

  position: relative;
  padding: 0;
  padding-top: 80px;

  // padding-top: $owned-media-section-padding-desktop;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 40%) 0%,
    rgb(227 243 255 / 40%) 33.17%,
    rgb(129 204 231 / 40%) 64.42%
  );

  // &::after {
  //   position: absolute;
  //   bottom: 0;
  //   left: 0;
  //   width: 100%;
  //   height: 96px;
  //   content: '';
  //   background-color: #f4f4f4;
  //   z-index: 1;
  // }

  &::before {
    position: absolute;
    top: 0;
    left: 50%;
    z-index: 0;
    width: 100%;
    height: 100%;
    max-height: 770px;
    content: '';
    background-image: url('#{$owned-media-image-path}/fv-main-illustration.png');
    background-repeat: no-repeat;
    background-position: center bottom;
    transform: translateX(-50%);
  }

  &__container {
    @include owned-media-container;

    display: flex;
    flex-direction: row;
    max-width: 1400px;
    height: $owned-media-fv-height;
    min-height: 600px;
    padding-top: 32px;

    // max-width: $owned-media-fv-container-max-width;
    // padding-left: 100px;

    .fv-left {
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: $owned-media-fv-left-width;
      height: fit-content;
      padding-top: $owned-media-section-padding-desktop;
      transform: rotate(-5deg);

      &__catch {
        position: relative;

        .catch-copy {
          position: relative;
          display: flex;
          flex-direction: column;
          gap: $owned-media-fv-catch-gap;
          align-items: center;
          justify-content: center;
          width: fit-content;
          margin-bottom: $owned-media-fv-catch-margin-bottom;

          @include fv-catch-decoration;

          &__text {
            font-size: $owned-media-fv-catch-font-size;
            font-weight: 700;
            line-height: 1.2;
            color: $owned-media-text-color;
            text-align: center;
          }

          &__char-dot {
            position: relative;
            display: inline-block;

            &::before {
              position: absolute;
              top: -5px;
              left: 50%;
              width: 5px;
              height: 5px;
              content: '';
              background-color: #333;
              border-radius: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }

      &__pill {
        display: flex;
        align-items: center;
        width: fit-content;
        height: $owned-media-fv-pill-height;
        margin-bottom: $owned-media-fv-pill-margin-bottom;

        .pill {
          padding: $owned-media-fv-pill-padding;
          font-size: $owned-media-fv-pill-font-size;
          font-weight: 700;
          line-height: 1;

          &-left {
            @include fv-pill-part(
              $owned-media-text-color,
              $owned-media-white,
              $owned-media-fv-pill-border-radius 0 0 $owned-media-fv-pill-border-radius,
              2
            );

            padding-right: 16px;
            border-right: none;

            &__text {
              color: $owned-media-white;
            }
          }

          &-right {
            @include fv-pill-part(
              $owned-media-white,
              $owned-media-text-color,
              0 $owned-media-fv-pill-border-radius $owned-media-fv-pill-border-radius 0
            );

            padding-left: 16px;
            border-left: none;

            &__text {
              color: $owned-media-text-color;
            }
          }
        }
      }

      &__message {
        display: flex;
        flex-direction: column;
        gap: $owned-media-fv-message-gap;
        align-items: center;
        justify-content: center;

        .message-text {
          vertical-align: middle;

          &.strong {
            @include fv-message-text(
              $owned-media-fv-message-strong-font-size,
              $owned-media-primary-color
            );
          }

          &.accent {
            @include fv-message-text(
              $owned-media-fv-message-accent-font-size,
              $owned-media-text-color
            );
          }

          &.normal {
            @include fv-message-text(
              $owned-media-fv-message-normal-font-size,
              $owned-media-text-color
            );
          }
        }
      }
    }

    .fv-right {
      position: absolute;
      top: 32px;
      right: 16px;
      display: flex;
      flex: 1;
      justify-content: flex-end;
      width: $owned-media-fv-left-width;

      &__form {
        .form-container {
          position: relative;
          z-index: 10;
          width: $owned-media-fv-form-width;
          height: 720px;
          padding: $owned-media-fv-form-padding;
          background-color: $owned-media-white;
          border-radius: $owned-media-fv-form-border-radius;
          box-shadow: 0 0 24px rgb(0 0 0 / 25%);

          &__text {
            font-size: $owned-media-fv-form-text-font-size;
            font-weight: 700;
          }
        }
      }
    }
  }

  @include media-breakpoint-down('xl') {
    position: relative;

    &::before {
      top: unset;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 530px;
      transform: translateX(30%);
    }

    height: 1200px;

    &__container {
      flex-direction: column;
      width: 100%;
      height: auto;
      padding: 0 16px;
    }

    .fv-left {
      width: 100%;
      height: auto;
      margin-bottom: $owned-media-spacing-xl;
    }

    .fv-right {
      position: relative;
      width: 100%;
      margin-bottom: $owned-media-section-padding-desktop;

      &__form {
        position: absolute;
        top: 0;
        left: 16px;
        display: none;
        display: flex;
        justify-content: flex-start;
        width: 100%;
      }
    }
  }

  @include media-breakpoint-down(md) {
    height: 813px;
    padding-top: $owned-media-spacing-xl;

    &::before {
      top: unset;
      bottom: 0;
      left: 50%;
      width: 100%;
      background-image: url('#{$owned-media-image-path}/fv-main-illustration-sp.png');
      background-position: center bottom;
      background-size: 100%;
      transform: translateX(-50%);
    }

    &__container {
      flex-direction: column;
      align-items: center;
      height: auto;
      padding-top: 0;
      padding-right: 0;
      padding-left: 0;

      .fv-left {
        justify-content: flex-start;
        width: 100%;
        height: 600px;
        padding: $owned-media-spacing-20;
        transform: none;

        &__catch {
          .catch-copy {
            margin-bottom: $owned-media-spacing-20;

            @include fv-mobile-catch-decoration;

            &__text {
              font-size: $owned-media-fv-mobile-catch-font-size;
            }
          }
        }

        &__pill {
          display: flex;
          justify-content: center;
          min-width: 100%;
          height: $owned-media-fv-mobile-pill-height;
          margin-bottom: $owned-media-spacing-20;

          .pill {
            padding: $owned-media-fv-mobile-pill-padding;
            font-size: $owned-media-fv-mobile-pill-font-size;
            border: $owned-media-fv-mobile-pill-border-width solid $owned-media-text-color;

            &-left {
              padding-right: 4px;
            }

            &-right {
              padding-left: 4px;
            }
          }
        }

        &__message {
          gap: $owned-media-fv-mobile-message-gap;

          .message-text {
            &.strong {
              font-size: $owned-media-fv-mobile-message-strong-font-size;
            }

            &.accent {
              font-size: $owned-media-fv-mobile-message-accent-font-size;
            }

            &.normal {
              font-size: $owned-media-fv-mobile-message-normal-font-size;
            }
          }
        }
      }

      .fv-right {
        display: none;
      }
    }

    // フォームを下部に表示
    &__form-mobile {
      display: block;
      width: 100%;
      padding: $owned-media-spacing-xl $owned-media-spacing-20;
      margin-top: $owned-media-spacing-xl;

      .form-container {
        width: 100%;
        max-width: $owned-media-fv-form-width;
        height: auto;
        padding: $owned-media-fv-mobile-form-padding;
        margin: 0 auto;

        &__text {
          font-size: $owned-media-fv-mobile-form-text-font-size;
        }
      }
    }
  }

  @include media-breakpoint-down(sm) {
    padding-top: 0;
  }
}
