/**
 * オウンドメディアサービスページ用JavaScript
 */

(function () {
  'use strict';

  // DOM読み込み完了後に実行
  document.addEventListener('DOMContentLoaded', function () {
    // プログレスマーク初期化
    initProgressMarks();

    // スムーススクロール初期化
    initSmoothScroll();

    // FAQアコーディオン初期化（将来実装用）
    initFAQAccordion();

    // 動的フォーム配置初期化
    initDynamicFormPlacement();

    // 高度な遅延読み込み初期化
    initAdvancedLazyLoading();

    // 画面サイズ変更時の再初期化（デバウンス付き）
    let resizeTimeout;
    window.addEventListener('resize', function () {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(function () {
        // 既存のアニメーションを停止
        const existingTracks = document.querySelectorAll(
          '.owned-media-partner-logos__pc-scrolling-track, .owned-media-partner-logos__scrolling-track',
        );
        existingTracks.forEach(function (track) {
          track.style.transform = '';
          track.style.willChange = '';
        });

        // リサイズ時は初期化フラグをリセットして再初期化を許可
        window.partnerLogosInitialized = false;

        // パートナーロゴの無限スクロールを再初期化
        initPartnerLogosScroll();

        // 再初期化完了フラグを設定
        window.partnerLogosInitialized = true;
      }, 250);
    });
  });

  // 画像を含むすべてのリソースが読み込まれた後に実行
  window.addEventListener('load', function () {
    // 重複初期化を防ぐためのフラグをチェック
    if (window.partnerLogosInitialized) {
      console.log('[Partner Logos] Already initialized, skipping...');
      return;
    }

    console.log('[Partner Logos] Window load event fired');
    initPartnerLogosScroll();

    // 初期化完了フラグを設定
    window.partnerLogosInitialized = true;
    console.log('[Partner Logos] Initialization completed');
  });

  /**
   * プログレスマーク初期化
   */
  function initProgressMarks() {
    const serviceItems = document.querySelectorAll(
      '.owned-media-service__item-number[data-progress]',
    );

    serviceItems.forEach(function (item) {
      const progressValue = parseInt(item.getAttribute('data-progress'));
      updateProgressMark(item, progressValue);
    });
  }

  /**
   * プログレスマークを更新
   * @param {Element} element - 番号要素
   * @param {number} activeStep - アクティブなステップ（1-7）
   */
  function updateProgressMark(element, activeStep) {
    // 7個のドットの背景画像を生成
    const dots = [];
    const backgroundLine = 'linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%)';

    for (let i = 1; i <= 7; i++) {
      if (i === activeStep) {
        // アクティブなドット：白い塗りつぶし + 緑の外周線（2px）
        dots.push(
          'radial-gradient(circle, #ffffff 4px, #ffffff 4px, #7dc8b6 4px, #7dc8b6 6px, transparent 6px)',
        );
      } else {
        // 非アクティブなドット：グレーの塗りつぶし
        dots.push('radial-gradient(circle, #d9d9d9 6px, transparent 6px)');
      }
    }

    // ドットを前面に、背景ラインを後面に配置
    const backgroundImages = [...dots, backgroundLine];

    // 背景位置を設定（ドット7個 + 背景ライン1個）
    const backgroundPositions = [
      '0 0', // ドット1
      '15px 0', // ドット2
      '30px 0', // ドット3
      '44px 0', // ドット4
      '59px 0', // ドット5
      '73px 0', // ドット6
      '88px 0', // ドット7
      '11px 11px', // 背景ライン
    ];

    // 背景サイズを設定
    const backgroundSizes = [
      '24px 24px', // ドット1
      '24px 24px', // ドット2
      '24px 24px', // ドット3
      '24px 24px', // ドット4
      '24px 24px', // ドット5
      '24px 24px', // ドット6
      '24px 24px', // ドット7
      '88px 3px', // 背景ライン
    ];

    // 擬似要素のスタイルを動的に設定
    const style = document.createElement('style');
    const uniqueClass =
      'progress-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);
    element.classList.add(uniqueClass);

    style.textContent = `
      .${uniqueClass}::before {
        background-image: ${backgroundImages.join(', ')};
        background-position: ${backgroundPositions.join(', ')};
        background-size: ${backgroundSizes.join(', ')};
      }
    `;

    document.head.appendChild(style);
  }

  /**
   * 提携企業ロゴの無限スクロール初期化
   */
  /**
   * 現在の表示状態（PC/モバイル）を判定
   */
  function getCurrentDisplayMode() {
    // モバイル版のコンテナが表示されているかチェック
    const mobileContainer = document.querySelector(
      '.owned-media-partner-logos__scrolling-container',
    );
    if (mobileContainer) {
      const mobileStyle = window.getComputedStyle(mobileContainer);
      if (mobileStyle.display !== 'none') {
        return 'mobile';
      }
    }

    // PC版のロゴラッパーが表示されているかチェック
    const pcWrapper = document.querySelector('.owned-media-partner-logos__logos-wrapper');
    if (pcWrapper) {
      const pcStyle = window.getComputedStyle(pcWrapper);
      if (pcStyle.display !== 'none') {
        return 'pc';
      }
    }

    // フォールバック：画面幅で判定
    return window.innerWidth >= 768 ? 'pc' : 'mobile';
  }

  function initPartnerLogosScroll() {
    const logoSection = document.querySelector('.owned-media-partner-logos');
    if (!logoSection) return;

    const currentMode = getCurrentDisplayMode();
    console.log(`[Partner Logos] Current display mode: ${currentMode}`);

    // PC版・モバイル版ともに無限スクロールを適用
    const logoTracks = document.querySelectorAll('.owned-media-partner-logos__track');

    logoTracks.forEach(function (track) {
      const rowType = track.getAttribute('data-row');
      console.log(
        `[${currentMode === 'mobile' ? 'Mobile' : 'PC'} Logo] Initializing track: ${rowType}, logos: ${track.children.length}`,
      );

      // 少し遅延させてDOM要素が完全に表示されてから初期化
      setTimeout(function () {
        initInfiniteScroll(track, currentMode);
      }, 100);
    });
  }

  /**
   * 無限スクロールの初期化（PC・モバイル対応版）
   * @param {HTMLElement} track - スクロールトラック要素
   * @param {string} displayMode - 表示モード（'mobile' または 'pc'）
   */
  function initInfiniteScroll(track, displayMode) {
    const originalLogos = Array.from(track.children);

    if (originalLogos.length === 0) return;

    // aria-hidden属性を追加（スクリーンリーダー対策）
    track.setAttribute('aria-hidden', 'true');

    console.log(
      `[${displayMode === 'mobile' ? 'Mobile' : 'PC'} Logo] 無限スクロール初期化: ${originalLogos.length}個のロゴ`,
    );

    // 無限スクロールアニメーションを開始
    startInfiniteScrollAnimation(track, originalLogos, displayMode);
  }

  /**
   * 最適化された無限スクロールアニメーション（DOM要素数最小化版）
   * @param {HTMLElement} track - スクロールトラック要素
   * @param {Array} originalLogos - 元のロゴ配列
   * @param {string} displayMode - 表示モード（'mobile' または 'pc'）
   */
  function startInfiniteScrollAnimation(track, originalLogos, displayMode) {
    // モジュロ演算方式に最適化された複製数：2セット分複製
    const cloneCount = 2; // 2セット分複製（モジュロ演算により効率的な無限ループを実現）
    const fragment = document.createDocumentFragment();

    for (let i = 0; i < cloneCount; i++) {
      originalLogos.forEach((logo) => {
        const clonedLogo = logo.cloneNode(true);
        clonedLogo.setAttribute('aria-hidden', 'true');
        fragment.appendChild(clonedLogo);
      });
    }

    track.appendChild(fragment);

    console.log(
      `[${displayMode === 'mobile' ? 'Mobile' : 'PC'} Logo] モジュロ演算方式: ${originalLogos.length}個の元ロゴ + ${originalLogos.length * cloneCount}個の複製 = ${originalLogos.length * (cloneCount + 1)}個の要素`,
    );

    // GPUアクセラレーションを有効化
    track.style.willChange = 'transform';
    track.style.transform = 'translateZ(0)';

    // 画像の読み込み完了を待ってから幅計算を実行
    function waitForImagesAndCalculateWidth() {
      const images = track.querySelectorAll('img');
      const imagePromises = Array.from(images).map((img) => {
        return new Promise((resolve) => {
          if (img.complete) {
            resolve();
          } else {
            img.addEventListener('load', resolve);
            img.addEventListener('error', resolve); // エラーでも続行
          }
        });
      });

      Promise.all(imagePromises).then(() => {
        // 1セットの幅をシンプルに計算（gapのみ、マージン削除）
        const computedStyle = window.getComputedStyle(track);
        const gap = parseInt(computedStyle.gap) || (displayMode === 'mobile' ? 30 : 25);

        // 各ロゴの実際の幅を計算（マージンなし）
        let totalLogoWidth = 0;
        for (let i = 0; i < originalLogos.length; i++) {
          const logo = track.children[i];
          if (logo) {
            const logoRect = logo.getBoundingClientRect();
            // 画像の実際の幅のみ（マージンなし）
            totalLogoWidth += logoRect.width;
          }
        }

        // 1セットの合計幅 = 全ロゴ幅 + ギャップ（ロゴ数-1個分）
        const totalGapWidth = (originalLogos.length - 1) * gap;
        const singleSetWidth = totalLogoWidth + totalGapWidth;

        console.log(
          `[${displayMode === 'mobile' ? 'Mobile' : 'PC'} Logo] 1セット幅計算（画像読み込み完了後）:`,
        );
        console.log(`  - ロゴ数: ${originalLogos.length}個`);
        console.log(`  - 総ロゴ幅: ${totalLogoWidth}px`);
        console.log(
          `  - 総ギャップ幅: ${totalGapWidth}px (${gap}px × ${originalLogos.length - 1}個)`,
        );
        console.log(`  - 1セット合計幅: ${singleSetWidth}px`);

        // アニメーション変数
        let currentPosition = 0;
        const scrollSpeed = displayMode === 'mobile' ? 50 : 40; // 本来の速度に戻す
        let lastTimestamp = 0;

        // 詳細デバッグログ用変数
        let frameCount = 0;
        let lastTransformValue = 0;
        let resetCount = 0;
        let jumpDetectionThreshold = 10; // 完全シームレス用：10px以上の変化を異常として検出
        let debugLogEnabled =
          window.location.hostname === 'localhost' || window.location.hostname.includes('dev');

        // 視覚的ジャンプ検出用の履歴
        let transformHistory = [];
        let maxHistoryLength = 10;
        let smoothnessThreshold = 5; // 完全シームレス用：5px以上の連続フレーム間変化を異常として検出

        // デバッグログ関数
        function logDebug(category, message, data = {}) {
          if (!debugLogEnabled) return;
          const timestamp = performance.now().toFixed(2);
          console.log(`[${timestamp}ms] [${category}] ${message}`, data);
        }

        // 完全シームレス検証：高度な視覚的ジャンプ検出関数
        function detectVisualJump(currentTransform, frameNumber, resetOccurred = false) {
          if (!debugLogEnabled || frameNumber < 2) return false;

          // transform履歴を更新
          transformHistory.push(currentTransform);
          if (transformHistory.length > maxHistoryLength) {
            transformHistory.shift();
          }

          if (transformHistory.length < 3) return false;

          const current = transformHistory[transformHistory.length - 1];
          const previous = transformHistory[transformHistory.length - 2];
          const beforePrevious = transformHistory[transformHistory.length - 3];

          // 連続フレーム間の変化量を計算
          const currentDelta = Math.abs(current - previous);
          const previousDelta = Math.abs(previous - beforePrevious);

          // 急激な変化の検出
          const isLargeJump = currentDelta > jumpDetectionThreshold;
          const isSuddenChange = currentDelta > previousDelta * 3; // 前フレームの3倍以上の変化
          const isUnsmooth = currentDelta > smoothnessThreshold;

          // 複数の条件で視覚的ジャンプを判定
          const jumpDetected = isLargeJump || (isSuddenChange && isUnsmooth);

          if (jumpDetected) {
            logDebug('ADVANCED_GRADUAL_VIOLATION', `🚨 高度な段階的制御違反検出`, {
              currentTransform: current.toFixed(2),
              previousTransform: previous.toFixed(2),
              currentDelta: currentDelta.toFixed(2),
              previousDelta: previousDelta.toFixed(2),
              isLargeJump: isLargeJump,
              isSuddenChange: isSuddenChange,
              isUnsmooth: isUnsmooth,
              frameNumber: frameNumber,
              transformHistory: transformHistory.slice(-5).map((v) => v.toFixed(2)),
              expectedBehavior: '段階的な滑らかな変化',
              actualResult: '急激な変化を検出',
              seamlessStatus: 'ADVANCED_GRADUAL_VIOLATION',
            });
          } else if (resetOccurred && currentDelta <= smoothnessThreshold) {
            logDebug('ADVANCED_GRADUAL_SUCCESS', `✅ 高度な段階的制御成功確認`, {
              currentDelta: currentDelta.toFixed(2),
              previousDelta: previousDelta.toFixed(2),
              smoothnessThreshold: smoothnessThreshold.toFixed(2),
              frameNumber: frameNumber,
              seamlessStatus: 'ADVANCED_GRADUAL_SUCCESS',
              smoothnessLevel: 'PERFECT',
            });
          }

          return jumpDetected;
        }

        // 詳細デバッグログ付き無限スクロールアニメーション
        function animate(timestamp) {
          if (!lastTimestamp) {
            lastTimestamp = timestamp;
            logDebug('ANIMATION_START', `アニメーション開始 - 1セット幅: ${singleSetWidth}px`);
          }

          frameCount++;
          const deltaTime = timestamp - lastTimestamp;
          const deltaPosition = (scrollSpeed * deltaTime) / 1000; // ミリ秒を秒に変換

          // フレーム毎の詳細ログ（100フレーム毎に出力）
          if (frameCount % 100 === 0) {
            logDebug('FRAME_STATE', `フレーム ${frameCount}`, {
              currentPosition: currentPosition.toFixed(2),
              deltaTime: deltaTime.toFixed(2),
              deltaPosition: deltaPosition.toFixed(4),
              scrollSpeed: scrollSpeed,
            });
          }

          currentPosition += deltaPosition;

          // シンプルな即座リセット（元の方式に戻す）
          const resetThreshold = singleSetWidth; // 1セット分移動したタイミング
          let resetOccurred = false;

          // シンプルリセット判定
          if (currentPosition >= resetThreshold) {
            const beforeReset = currentPosition;

            // 即座にリセット実行
            currentPosition = currentPosition - singleSetWidth;
            resetOccurred = true;
            resetCount++;

            logDebug('SIMPLE_RESET_COMPLETED', `✅ シンプルリセット完了 ${resetCount}回目`, {
              beforeReset: beforeReset.toFixed(4),
              afterReset: currentPosition.toFixed(4),
              resetDistance: singleSetWidth.toFixed(4),
              resetType: 'SIMPLE_IMMEDIATE_RESET',
              frameCount: frameCount,
            });
          }

          // transform値の計算と適用
          const transformValue = currentPosition;
          const transformDifference = Math.abs(transformValue - lastTransformValue);

          // シンプルリセット検証：視覚的ジャンプ検出
          if (transformDifference > jumpDetectionThreshold && frameCount > 1) {
            logDebug('SIMPLE_RESET_VIOLATION', `🚨 視覚的ジャンプを検出`, {
              previousTransform: lastTransformValue.toFixed(2),
              currentTransform: transformValue.toFixed(2),
              difference: transformDifference.toFixed(2),
              threshold: jumpDetectionThreshold,
              resetOccurred: resetOccurred,
              frameCount: frameCount,
              resetType: 'SIMPLE_IMMEDIATE_RESET',
            });
          } else if (resetOccurred) {
            logDebug('SIMPLE_RESET_SUCCESS', `✅ シンプルリセット成功確認`, {
              previousTransform: lastTransformValue.toFixed(2),
              currentTransform: transformValue.toFixed(2),
              difference: transformDifference.toFixed(2),
              threshold: jumpDetectionThreshold.toFixed(2),
              frameCount: frameCount,
              resetType: 'SIMPLE_IMMEDIATE_RESET',
            });
          }

          // 完全シームレス検証：高度な視覚的ジャンプ検出
          detectVisualJump(transformValue, frameCount, resetOccurred);

          // transform を適用
          track.style.transform = `translate3d(-${transformValue}px, 0, 0)`;

          // 次フレーム用に値を保存
          lastTransformValue = transformValue;
          lastTimestamp = timestamp;

          // 詳細ログ（リセット発生時は必ず出力）
          if (resetOccurred || frameCount % 200 === 0) {
            logDebug('TRANSFORM_APPLIED', `Transform適用`, {
              transformValue: transformValue.toFixed(2),
              currentPosition: currentPosition.toFixed(2),
              resetOccurred: resetOccurred,
              frameCount: frameCount,
            });
          }

          requestAnimationFrame(animate);
        }

        // リアルタイム状態監視機能
        let monitoringInterval;
        function startRealtimeMonitoring() {
          if (!debugLogEnabled) return;

          monitoringInterval = setInterval(() => {
            const currentTransform = track.style.transform;
            const transformMatch = currentTransform.match(/translate3d\(-?([0-9.]+)px/);
            const currentTransformValue = transformMatch ? parseFloat(transformMatch[1]) : 0;

            logDebug('REALTIME_MONITOR', `リアルタイム状態監視`, {
              currentPosition: currentPosition.toFixed(2),
              transformValue: currentTransformValue.toFixed(2),
              frameCount: frameCount,
              resetCount: resetCount,
              singleSetWidth: singleSetWidth.toFixed(2),
              progressPercentage: (
                ((currentPosition % singleSetWidth) / singleSetWidth) *
                100
              ).toFixed(1),
              nextResetDistance: (singleSetWidth * 2 - currentPosition).toFixed(2),
            });
          }, 2000); // 2秒毎に監視
        }

        function stopRealtimeMonitoring() {
          if (monitoringInterval) {
            clearInterval(monitoringInterval);
            monitoringInterval = null;
          }
        }

        // アニメーション開始
        requestAnimationFrame(animate);

        // リアルタイム監視開始
        startRealtimeMonitoring();

        // ページ離脱時にリアルタイム監視を停止
        window.addEventListener('beforeunload', stopRealtimeMonitoring);
      });
    }

    // 画像読み込み完了を待ってから幅計算を実行
    waitForImagesAndCalculateWidth();
  }

  /**
   * スムーススクロール初期化
   */
  function initSmoothScroll() {
    const ctaButtons = document.querySelectorAll(
      '.owned-media__cta-form-wrapper, .owned-media__fv-form-wrapper',
    );

    ctaButtons.forEach(function (button) {
      button.addEventListener('click', function (e) {
        // 実際のフォームが実装されるまでは、お問い合わせセクションへスクロール
        const contactSection = document.querySelector('.section_contact');
        if (contactSection) {
          e.preventDefault();
          contactSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      });
    });
  }

  /**
   * FAQアコーディオン初期化（将来実装用）
   */
  function initFAQAccordion() {
    const faqItems = document.querySelectorAll('.owned-media__faq-item');

    faqItems.forEach(function (item) {
      const question = item.querySelector('.owned-media__faq-question');
      const answer = item.querySelector('.owned-media__faq-answer');

      if (question && answer) {
        question.addEventListener('click', function () {
          const isOpen = item.classList.contains('is-open');

          // 他のFAQを閉じる
          faqItems.forEach(function (otherItem) {
            otherItem.classList.remove('is-open');
            const otherAnswer = otherItem.querySelector('.owned-media__faq-answer');
            if (otherAnswer) {
              otherAnswer.style.maxHeight = '0';
            }
          });

          // クリックされたFAQの開閉
          if (!isOpen) {
            item.classList.add('is-open');
            answer.style.maxHeight = answer.scrollHeight + 'px';
          }
        });
      }
    });
  }

  /**
   * パフォーマンス最適化：debounce関数
   */
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = function () {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * モバイルデバイス検出
   */
  function isMobileDevice() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent,
    );
  }

  /**
   * 動的フォーム配置初期化
   */
  function initDynamicFormPlacement() {
    // フォームが配置された後に実行
    setTimeout(function () {
      const originalFormContainer = document.querySelector('.section__contact__cont');
      const topFormContainer = document.getElementById('top-form-container');

      if (!originalFormContainer || !topFormContainer) return;

      // フォーム要素を探す（BowNowフォームはscriptタグまたはiframeとして挿入される）
      let formElement = null;

      // scriptタグ、iframe、その他のフォーム要素を探す
      const possibleFormElements = originalFormContainer.querySelectorAll(
        'script, iframe, form, div[id*="bownow"], div[class*="bownow"]',
      );

      if (possibleFormElements.length > 0) {
        // フォーム関連要素をすべて取得
        formElement = document.createElement('div');
        formElement.className = 'bownow-form-wrapper';

        // 元のコンテナの全内容を保存
        formElement.innerHTML = originalFormContainer.innerHTML;
      }

      if (!formElement) return;

      // フォームの現在位置を追跡
      let formIsInTop = false;
      // フォーム移動を一時停止するフラグ
      let formMovementPaused = false;

      // フォームを移動する関数（改善版）
      function moveFormTo(targetContainer, sourceContainer) {
        // フォーム移動が一時停止中の場合は何もしない
        if (formMovementPaused) {
          console.log('[Form Movement] 移動が一時停止中のため、フォーム移動をスキップします');
          return;
        }

        // コンテナの存在確認
        if (!targetContainer || !sourceContainer) {
          console.log('[Form Movement] コンテナが見つからないため、フォーム移動をスキップします');
          return;
        }

        // フォーム送信中の場合は移動を避ける（追加の安全措置）
        const isFormSubmitting =
          sourceContainer.querySelector('.show-page-btn .btn[disabled]') !== null ||
          targetContainer.querySelector('.show-page-btn .btn[disabled]') !== null;
        if (isFormSubmitting) {
          console.log('[Form Movement] フォーム送信中のため、フォーム移動をスキップします');
          return;
        }

        // DOM要素を安全に移動（イベントリスナーを保持）
        try {
          while (sourceContainer.firstChild) {
            targetContainer.appendChild(sourceContainer.firstChild);
          }
        } catch (error) {
          console.error('[Form Movement] フォーム移動中にエラーが発生しました:', error);
        }
      }

      // フォーム送信イベントの監視（軽量化版）
      function setupFormSubmissionHandling() {
        // フォーム送信時の処理（最小限の干渉）
        function handleFormSubmission() {
          console.log('[Form Submission] フォーム送信を検出しました');

          // フォーム移動を一時停止（送信処理を阻害しないよう最小限の処理）
          formMovementPaused = true;
          console.log('[Form Movement] フォーム移動を一時停止しました');

          // 送信処理を阻害しないため、スクロール防止処理は削除
          // 外部フォーム（BowNow）の正常な動作を優先

          // 短時間後にフォーム移動を再開（送信処理完了を想定）
          setTimeout(function () {
            formMovementPaused = false;
            console.log('[Form Movement] フォーム移動を再開しました');

            // 現在のスクロール位置に基づいてフォーム位置を調整
            handleScroll();
          }, 2000); // 2秒後に再開（処理時間を短縮）
        }

        // 軽量な監視設定（重複を避けるため一度だけ設定）
        const monitorFormSubmission = function () {
          // 送信ボタンのクリックイベントのみ監視（最小限の干渉）
          const submitButtons = document.querySelectorAll(
            'input[type="submit"], button[type="submit"], .btn[class*="submit"], [id*="submit"]',
          );

          submitButtons.forEach(function (button) {
            // 重複登録を防ぐためのフラグをチェック
            if (!button.hasAttribute('data-form-monitor-attached')) {
              button.setAttribute('data-form-monitor-attached', 'true');
              button.addEventListener('click', handleFormSubmission, {
                once: false,
                passive: true,
              });
            }
          });
        };

        // 初期監視設定
        monitorFormSubmission();

        // DOM変更を監視（軽量化）
        const observer = new MutationObserver(function (mutations) {
          let shouldReMonitor = false;

          mutations.forEach(function (mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
              mutation.addedNodes.forEach(function (node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  // 新しく追加された送信ボタンのみ監視
                  const hasSubmitElements =
                    node.querySelectorAll &&
                    node.querySelectorAll('input[type="submit"], button[type="submit"], .btn')
                      .length > 0;

                  if (hasSubmitElements) {
                    shouldReMonitor = true;
                  }
                }
              });
            }
          });

          // 必要な場合のみ再監視
          if (shouldReMonitor) {
            setTimeout(monitorFormSubmission, 100);
          }
        });

        // DOM全体を監視（軽量化）
        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });
      }

      // 初期配置（ページ上部に配置）
      moveFormTo(topFormContainer, originalFormContainer);
      formIsInTop = true;

      // スクロールイベントハンドラー（改善版）
      const handleScroll = debounce(function () {
        // フォーム移動が一時停止中の場合は何もしない
        if (formMovementPaused) {
          console.log(
            '[Scroll Handler] フォーム移動が一時停止中のため、スクロール処理をスキップします',
          );
          return;
        }

        // フォーム送信中かどうかをチェック（追加の安全措置）
        const isFormSubmitting = document.querySelector('.show-page-btn .btn[disabled]') !== null;
        if (isFormSubmitting) {
          console.log('[Scroll Handler] フォーム送信中のため、スクロール処理をスキップします');
          return;
        }

        const scrollPosition = window.scrollY || document.documentElement.scrollTop;
        const documentHeight = document.documentElement.scrollHeight;
        const halfwayPoint = documentHeight / 2;

        // スクロール位置が画面の半分を超えたら
        if (scrollPosition > halfwayPoint && formIsInTop) {
          // フォームを元の位置（下部）に戻す
          moveFormTo(originalFormContainer, topFormContainer);
          formIsInTop = false;
          console.log('[Form Movement] フォームを下部に移動しました');
        } else if (scrollPosition <= halfwayPoint && !formIsInTop) {
          // フォームを上部に移動
          moveFormTo(topFormContainer, originalFormContainer);
          formIsInTop = true;
          console.log('[Form Movement] フォームを上部に移動しました');
        }
      }, 150);

      // スクロールイベントをリッスン
      window.addEventListener('scroll', handleScroll);

      // フォーム送信処理の設定
      setupFormSubmissionHandling();

      // 初期スクロール位置をチェック
      handleScroll();
    }, 1000); // BowNowフォームの読み込みを待つ
  }

  /**
   * タッチデバイス用の最適化
   */
  function initTouchOptimizations() {
    if (isMobileDevice()) {
      // タッチデバイス用のクラスを追加
      document.documentElement.classList.add('touch-device');

      // iOS Safariでの100vh問題を解決
      const setVH = function () {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', vh + 'px');
      };

      setVH();
      window.addEventListener('resize', debounce(setVH, 250));
    }
  }

  /**
   * 高度な遅延読み込み初期化
   */
  function initAdvancedLazyLoading() {
    // Intersection Observer APIをサポートしているかチェック
    if (!('IntersectionObserver' in window)) {
      console.log(
        '[Lazy Loading] IntersectionObserver not supported, falling back to immediate loading',
      );
      return;
    }

    // 遅延読み込み対象の画像を取得
    const lazyImages = document.querySelectorAll('img[loading="lazy"]');

    if (lazyImages.length === 0) {
      return;
    }

    // Intersection Observerの設定
    const imageObserver = new IntersectionObserver(
      function (entries, observer) {
        entries.forEach(function (entry) {
          if (entry.isIntersecting) {
            const img = entry.target;

            // 画像の読み込み開始
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }

            // 読み込み完了時の処理
            img.addEventListener('load', function () {
              img.classList.add('loaded');
            });

            // 監視を停止
            observer.unobserve(img);
          }
        });
      },
      {
        // ビューポートの50px手前で読み込み開始
        rootMargin: '50px 0px',
        threshold: 0.01,
      },
    );

    // 各画像を監視対象に追加
    lazyImages.forEach(function (img) {
      imageObserver.observe(img);
    });

    console.log(`[Lazy Loading] ${lazyImages.length}個の画像を遅延読み込み対象に設定しました`);
  }

  /**
   * パフォーマンス監視機能
   */
  function initPerformanceMonitoring() {
    // Performance Observer APIをサポートしているかチェック
    if (!('PerformanceObserver' in window)) {
      return;
    }

    // LCP (Largest Contentful Paint) を監視
    const lcpObserver = new PerformanceObserver(function (list) {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log(`[Performance] LCP: ${lastEntry.startTime.toFixed(2)}ms`);
    });

    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (_error) {
      // サポートされていない場合は無視
      console.log('[Performance] LCP monitoring not supported');
    }

    // FID (First Input Delay) を監視
    const fidObserver = new PerformanceObserver(function (list) {
      const entries = list.getEntries();
      entries.forEach(function (entry) {
        console.log(`[Performance] FID: ${entry.processingStart - entry.startTime}ms`);
      });
    });

    try {
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (_error) {
      // サポートされていない場合は無視
      console.log('[Performance] FID monitoring not supported');
    }
  }

  /**
   * デバッグ用：無限スクロールの状態を確認
   */
  function debugInfiniteScroll() {
    const tracks = document.querySelectorAll('.owned-media-partner-logos__track');

    console.log('=== 無限スクロール デバッグ情報（最適化版） ===');

    tracks.forEach((track, index) => {
      const rowType = track.getAttribute('data-row');
      console.log(`Track ${index + 1} (${rowType}):`);
      console.log(`  - 子要素数: ${track.children.length}`);
      console.log(`  - offsetWidth: ${track.offsetWidth}px`);
      console.log(`  - scrollWidth: ${track.scrollWidth}px`);
      console.log(`  - transform: ${track.style.transform}`);
    });

    console.log('=== デバッグ情報終了 ===');
  }

  /**
   * パフォーマンス測定：DOM要素数とメモリ使用量
   */
  function measurePerformance() {
    const tracks = document.querySelectorAll('.owned-media-partner-logos__track');
    let totalElements = 0;

    tracks.forEach((track) => {
      totalElements += track.children.length;
    });

    console.log('=== パフォーマンス測定結果 ===');
    console.log(`総DOM要素数: ${totalElements}個`);

    if (performance.memory) {
      console.log(`使用メモリ: ${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
      console.log(`総メモリ: ${(performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
    }

    console.log('=== 測定終了 ===');
  }

  // パフォーマンス監視を初期化（開発環境のみ）
  if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    initPerformanceMonitoring();

    // 無限スクロール初期化後にパフォーマンス測定
    setTimeout(() => {
      measurePerformance();
    }, 2000);
  }

  // デバッグ関数をグローバルに公開
  window.debugInfiniteScroll = debugInfiniteScroll;

  // タッチ最適化を初期化
  initTouchOptimizations();
})();
