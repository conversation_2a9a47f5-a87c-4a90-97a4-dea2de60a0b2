/**
 * オウンドメディアページのパフォーマンステスト
 * 
 * このファイルは開発環境でのパフォーマンス測定とテストを行うためのものです。
 * ブラウザの開発者ツールのコンソールで実行してください。
 */

(function() {
  'use strict';

  /**
   * パフォーマンステストスイート
   */
  const PerformanceTestSuite = {
    
    /**
     * 無限スクロールのパフォーマンステスト
     */
    testInfiniteScrollPerformance: function() {
      console.log('=== 無限スクロール パフォーマンステスト開始 ===');
      
      const startTime = performance.now();
      let frameCount = 0;
      let lastFrameTime = startTime;
      const frameTimes = [];
      
      // 60フレーム（約1秒）のパフォーマンスを測定
      function measureFrame() {
        const currentTime = performance.now();
        const frameTime = currentTime - lastFrameTime;
        frameTimes.push(frameTime);
        frameCount++;
        lastFrameTime = currentTime;
        
        if (frameCount < 60) {
          requestAnimationFrame(measureFrame);
        } else {
          // 結果を分析
          const avgFrameTime = frameTimes.reduce((a, b) => a + b, 0) / frameTimes.length;
          const maxFrameTime = Math.max(...frameTimes);
          const minFrameTime = Math.min(...frameTimes);
          const fps = 1000 / avgFrameTime;
          
          console.log(`平均フレーム時間: ${avgFrameTime.toFixed(2)}ms`);
          console.log(`最大フレーム時間: ${maxFrameTime.toFixed(2)}ms`);
          console.log(`最小フレーム時間: ${minFrameTime.toFixed(2)}ms`);
          console.log(`平均FPS: ${fps.toFixed(2)}`);
          
          // 60FPS（16.67ms）を基準とした評価
          if (avgFrameTime <= 16.67) {
            console.log('✅ 無限スクロールのパフォーマンスは良好です');
          } else if (avgFrameTime <= 33.33) {
            console.log('⚠️ 無限スクロールのパフォーマンスは普通です（30FPS程度）');
          } else {
            console.log('❌ 無限スクロールのパフォーマンスに問題があります');
          }
          
          console.log('=== 無限スクロール パフォーマンステスト終了 ===\n');
        }
      }
      
      requestAnimationFrame(measureFrame);
    },

    /**
     * 画像読み込みパフォーマンステスト
     */
    testImageLoadingPerformance: function() {
      console.log('=== 画像読み込み パフォーマンステスト開始 ===');
      
      const images = document.querySelectorAll('img');
      let loadedImages = 0;
      let totalImages = images.length;
      const startTime = performance.now();
      
      console.log(`総画像数: ${totalImages}`);
      
      images.forEach(function(img, index) {
        if (img.complete) {
          loadedImages++;
        } else {
          img.addEventListener('load', function() {
            loadedImages++;
            const loadTime = performance.now() - startTime;
            console.log(`画像 ${index + 1}/${totalImages} 読み込み完了: ${loadTime.toFixed(2)}ms`);
            
            if (loadedImages === totalImages) {
              const totalLoadTime = performance.now() - startTime;
              console.log(`全画像読み込み完了: ${totalLoadTime.toFixed(2)}ms`);
              
              if (totalLoadTime <= 3000) {
                console.log('✅ 画像読み込み速度は良好です');
              } else if (totalLoadTime <= 5000) {
                console.log('⚠️ 画像読み込み速度は普通です');
              } else {
                console.log('❌ 画像読み込み速度に問題があります');
              }
              
              console.log('=== 画像読み込み パフォーマンステスト終了 ===\n');
            }
          });
          
          img.addEventListener('error', function() {
            console.error(`画像 ${index + 1} の読み込みに失敗しました: ${img.src}`);
          });
        }
      });
      
      if (loadedImages === totalImages) {
        console.log('✅ 全画像が既に読み込み済みです');
        console.log('=== 画像読み込み パフォーマンステスト終了 ===\n');
      }
    },

    /**
     * メモリ使用量テスト
     */
    testMemoryUsage: function() {
      console.log('=== メモリ使用量テスト開始 ===');
      
      if (performance.memory) {
        const memory = performance.memory;
        console.log(`使用中ヒープサイズ: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`総ヒープサイズ: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`ヒープサイズ制限: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
        
        const usagePercentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
        
        if (usagePercentage <= 50) {
          console.log('✅ メモリ使用量は良好です');
        } else if (usagePercentage <= 80) {
          console.log('⚠️ メモリ使用量は普通です');
        } else {
          console.log('❌ メモリ使用量が高すぎます');
        }
      } else {
        console.log('❌ メモリ情報を取得できません（Chrome以外のブラウザ）');
      }
      
      console.log('=== メモリ使用量テスト終了 ===\n');
    },

    /**
     * DOM要素数テスト
     */
    testDOMComplexity: function() {
      console.log('=== DOM複雑度テスト開始 ===');
      
      const totalElements = document.querySelectorAll('*').length;
      const images = document.querySelectorAll('img').length;
      const scripts = document.querySelectorAll('script').length;
      const styles = document.querySelectorAll('style, link[rel="stylesheet"]').length;
      
      console.log(`総DOM要素数: ${totalElements}`);
      console.log(`画像要素数: ${images}`);
      console.log(`スクリプト要素数: ${scripts}`);
      console.log(`スタイル要素数: ${styles}`);
      
      if (totalElements <= 1500) {
        console.log('✅ DOM複雑度は良好です');
      } else if (totalElements <= 3000) {
        console.log('⚠️ DOM複雑度は普通です');
      } else {
        console.log('❌ DOM複雑度が高すぎます');
      }
      
      console.log('=== DOM複雑度テスト終了 ===\n');
    },

    /**
     * 全テストを実行
     */
    runAllTests: function() {
      console.log('🚀 オウンドメディアページ パフォーマンステスト開始\n');
      
      this.testDOMComplexity();
      this.testMemoryUsage();
      this.testImageLoadingPerformance();
      
      // 無限スクロールテストは最後に実行（時間がかかるため）
      setTimeout(() => {
        this.testInfiniteScrollPerformance();
      }, 1000);
    }
  };

  /**
   * ユーティリティ関数
   */
  const TestUtils = {
    
    /**
     * ページ読み込み時間を測定
     */
    measurePageLoadTime: function() {
      if (performance.timing) {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        console.log(`ページ読み込み時間: ${loadTime}ms`);
        
        if (loadTime <= 3000) {
          console.log('✅ ページ読み込み速度は良好です');
        } else if (loadTime <= 5000) {
          console.log('⚠️ ページ読み込み速度は普通です');
        } else {
          console.log('❌ ページ読み込み速度に問題があります');
        }
      }
    },

    /**
     * Core Web Vitals を測定
     */
    measureCoreWebVitals: function() {
      console.log('=== Core Web Vitals 測定開始 ===');
      
      // LCP測定
      if ('PerformanceObserver' in window) {
        const lcpObserver = new PerformanceObserver(function(list) {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log(`LCP (Largest Contentful Paint): ${lastEntry.startTime.toFixed(2)}ms`);
          
          if (lastEntry.startTime <= 2500) {
            console.log('✅ LCPは良好です');
          } else if (lastEntry.startTime <= 4000) {
            console.log('⚠️ LCPは改善が必要です');
          } else {
            console.log('❌ LCPに問題があります');
          }
        });
        
        try {
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (e) {
          console.log('LCP測定はサポートされていません');
        }
      }
      
      console.log('=== Core Web Vitals 測定終了 ===\n');
    }
  };

  // グローバルに公開
  window.OwnedMediaPerformanceTest = PerformanceTestSuite;
  window.OwnedMediaTestUtils = TestUtils;

  // ページ読み込み完了後に自動実行
  if (document.readyState === 'complete') {
    TestUtils.measurePageLoadTime();
    TestUtils.measureCoreWebVitals();
  } else {
    window.addEventListener('load', function() {
      TestUtils.measurePageLoadTime();
      TestUtils.measureCoreWebVitals();
    });
  }

  console.log('📊 パフォーマンステストツールが読み込まれました');
  console.log('使用方法:');
  console.log('- OwnedMediaPerformanceTest.runAllTests() : 全テストを実行');
  console.log('- OwnedMediaPerformanceTest.testInfiniteScrollPerformance() : 無限スクロールテスト');
  console.log('- OwnedMediaPerformanceTest.testImageLoadingPerformance() : 画像読み込みテスト');
  console.log('- OwnedMediaTestUtils.measureCoreWebVitals() : Core Web Vitals測定');

})();
